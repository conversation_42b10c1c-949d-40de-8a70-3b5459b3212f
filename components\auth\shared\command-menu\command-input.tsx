'use client';
 
import * as React from 'react';
import { forwardRef } from "react"
import {
  AnimatePresence,
  motion,
} from 'motion/react';
import type { HTMLMotionProps, Transition } from 'motion/react';
import { ArrowRight, Check } from 'lucide-react';
import { cn } from '../../../../lib/utils';
 
type CommandInputContextType = {
  showInput: boolean;
  setShowInput: React.Dispatch<React.SetStateAction<boolean>>;
  transition: Transition;
  id: string;
};

const CommandInputContext = React.createContext<
  CommandInputContextType | undefined
>(undefined);
 
const useCommandInput = (): CommandInputContextType => {
  const context = React.useContext(CommandInputContext);
  if (!context) {
    throw new Error('useCommandInput must be used within a CommandInput');
  }
  return context;
};
 
type CommandInputProviderProps = React.ComponentProps<'div'> &
  Partial<CommandInputContextType>;
 
function CommandInputProvider({
  className,
  transition = { type: 'spring', stiffness: 400, damping: 30 },
  showInput,
  setShowInput,
  id,
  ...props
}: CommandInputProviderProps) {
  const localId = React.useId();
  const [localShowInput, setLocalShowInput] = React.useState(false);
 
  return (
    <CommandInputContext.Provider
      value={{
        showInput: showInput ?? localShowInput,
        setShowInput: setShowInput ?? setLocalShowInput,
        transition,
        id: id ?? localId,
      }}
    >
      <div
        data-slot="command-input-provider"
        className={cn(
          'relative w-full flex items-center justify-center h-12 select-none px-3 text-sm leading-8 transition-all duration-200 overflow-hidden',
          className,
        )}
        {...props}
      />
    </CommandInputContext.Provider>
  );
}
 
type CommandInputProps = HTMLMotionProps<'div'>;

function CommandInput({ className, ...props }: CommandInputProps) {
  return (
    <motion.div
      data-slot="command-input"
      className={cn(
        'flex size-full font-manrope_1 text-sm text-zinc-800 dark:text-zinc-50',
        className
      )}
      {...props}
    />
  );
}
 
type CommandInputFieldProps = HTMLMotionProps<'input'> & {
  placeholder?: string;
  value?: string;
  onChange?: (e: React.ChangeEvent<HTMLInputElement>) => void;
  onKeyDown?: (e: React.KeyboardEvent<HTMLInputElement>) => void;
  onEnterPress?: () => void; // Add onEnterPress prop
};
 
const CommandInputField = forwardRef<HTMLInputElement, CommandInputFieldProps>(
  ({ className, placeholder, value, onChange, onKeyDown, onEnterPress, ...props }, ref) => {
    const { transition, id } = useCommandInput();
    const internalRef = React.useRef<HTMLInputElement>(null);
    const inputRef = (ref as React.RefObject<HTMLInputElement>) || internalRef;

    // FIXED: Simple initial focus without aggressive refocusing
    React.useEffect(() => {
      if (inputRef.current && props.autoFocus) {
        const input = inputRef.current;

        const ensureActive = () => {
          if (input && document.activeElement !== input) {
            console.log('🔄 CommandInputField initial focus');
            input.focus();
            const length = input.value.length;
            input.setSelectionRange(length, length);
          }
        };

        // Initial focus only
        ensureActive();

        // Ensure focus after a short delay to handle any re-renders
        const timer = setTimeout(ensureActive, 100);

        return () => clearTimeout(timer);
      }
    }, [props.autoFocus]); // Only depend on autoFocus, not value

    const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
      console.log('🔥 CommandInputField keydown:', e.key, 'onEnterPress exists:', !!onEnterPress, 'target:', e.target, 'currentTarget:', e.currentTarget);

      if (e.key === 'Enter') {
        console.log('🔥 Enter key detected in CommandInputField!');

        if (onEnterPress) {
          console.log('🔥 onEnterPress exists, calling it now');
          e.preventDefault();
          e.stopPropagation();
          onEnterPress();
          return; // Don't call the original onKeyDown for Enter
        } else {
          console.error('❌ onEnterPress is not available!');
        }
      }

      // Call the original onKeyDown if provided (for non-Enter keys)
      if (onKeyDown) {
        onKeyDown(e);
      }
    };

    return (
      <motion.input
        ref={inputRef}
        data-slot="command-input-field"
        className={cn(
          'flex-1 bg-transparent border-none outline-none text-sm placeholder:text-zinc-500 dark:placeholder:text-zinc-400 text-zinc-900 dark:text-zinc-100 font-manrope_1',
          className,
        )}
        layoutId={`command-input-field-${id}`}
        transition={transition}
        placeholder={placeholder}
        value={value}
        onChange={onChange}
        onKeyDown={handleKeyDown}
        onMouseDown={(e) => {
          // Ensure the input is ready for keyboard events when clicked
          console.log('🖱️ CommandInputField mousedown - preparing for keyboard events');
          e.currentTarget.focus();
        }}
        onClick={(e) => {
          // Ensure the input is fully activated when clicked
          console.log('🖱️ CommandInputField clicked - ensuring full activation');
          e.currentTarget.focus();
          const length = e.currentTarget.value.length;
          e.currentTarget.setSelectionRange(length, length);
        }}
        onFocus={(e) => {
          console.log('🎯 CommandInputField onFocus triggered - ensuring keyboard activation');
          // Ensure the input is ready for keyboard events
          e.target.setAttribute('tabindex', '0');
          e.target.style.pointerEvents = 'auto';

          // CRITICAL: Trigger a synthetic interaction to ensure keyboard events work
          setTimeout(() => {
            if (e.target === document.activeElement) {
              // Simulate a click to ensure the input is truly active for keyboard events
              const clickEvent = new MouseEvent('click', {
                bubbles: true,
                cancelable: true,
                view: window
              });
              e.target.dispatchEvent(clickEvent);

              // Ensure focus is maintained after the synthetic click
              e.target.focus();
              const length = e.target.value.length;
              e.target.setSelectionRange(length, length);

              console.log('✅ Input fully activated for keyboard events');
            }
          }, 10);
        }}
        onBlur={(e) => {
          console.log('⚠️ CommandInputField onBlur triggered');
        }}
        autoFocus
        tabIndex={0}
        // Ensure the input is properly configured for keyboard interaction
        role="textbox"
        aria-label="Command input"
        spellCheck={false}
        autoComplete="off"
        autoCorrect="off"
        autoCapitalize="off"
        {...props}
      />
    );
  }
);
CommandInputField.displayName = 'CommandInputField';

type CommandInputSubmitProps = HTMLMotionProps<'button'> & {
  icon?: React.ElementType;
  message?: string;
  messageType?: 'success' | 'error' | '';
  isSubmitting?: boolean;
  success?: boolean;
  children?: React.ReactNode;
};
 
function CommandInputSubmit({
  className,
  children,
  icon: Icon = ArrowRight,
  message,
  messageType,
  isSubmitting,
  success,
  ...props
}: CommandInputSubmitProps) {
  const { transition, id } = useCommandInput();

  // Auto-reset success checkmark after 3 seconds
  React.useEffect(() => {
    if (messageType === 'success') {
      const timer = setTimeout(() => {
        // This will be handled by the parent component to reset messageType
      }, 4000);
      return () => clearTimeout(timer);
    }
  }, [messageType]);

  // Determine if we should show the message state (expanded button)
  // For success, we'll show checkmark without expanding
  const showExpandedMessage = message && messageType === 'error'; // Only expand for errors
  const showSuccessCheckmark = messageType === 'success';

  // Get background classes based on message type
  const getBackgroundClasses = () => {
    if (showSuccessCheckmark) {
      return "bg-gradient-to-br from-green-100 via-green-200 to-green-300 text-green-800 shadow-[0_-1px_0_1px_rgba(255,255,255,0.8)_inset,0_0_0_1px_rgb(34,197,94,0.3)_inset,0_0.5px_0_1.5px_rgba(34,197,94,0.2)_inset] dark:bg-gradient-to-r dark:from-green-900 dark:via-green-800 dark:to-green-700 dark:text-green-100 dark:shadow-[0_-1px_0_1px_rgba(0,0,0,0.8)_inset,0_0_0_1px_rgba(34,197,94,0.3)_inset,0_0.5px_0_1.5px_rgba(34,197,94,0.2)_inset]";
    }
    if (messageType === 'error') {
      return "bg-gradient-to-br from-zinc-100 via-zinc-200 to-red-50/40 text-red-800 shadow-[0_-1px_0_1px_rgba(255,255,255,0.8)_inset,0_0_0_1px_rgb(239,68,68,0.3)_inset,0_0.5px_0_1.5px_rgba(239,68,68,0.2)_inset] dark:bg-gradient-to-r dark:from-[#212026] dark:via-[#212026] dark:to-red-950/40 dark:text-red-300 dark:shadow-[0_-1px_0_1px_rgba(0,0,0,0.8)_inset,0_0_0_1px_rgba(239,68,68,0.3)_inset,0_0.5px_0_1.5px_rgba(239,68,68,0.2)_inset]";
    }
    // Default background - same as InputButton
    return "bg-gradient-to-br from-zinc-100 to-zinc-200 text-zinc-800 shadow-[0_-1px_0_1px_rgba(255,255,255,0.8)_inset,0_0_0_1px_rgb(220,220,220)_inset,0_0.5px_0_1.5px_#ccc_inset] dark:bg-gradient-to-b dark:from-[#212026] dark:via-[#212026] dark:to-[#29282e] dark:text-zinc-50 dark:shadow-[0_-1px_0_1px_rgba(0,0,0,0.8)_inset,0_0_0_1px_rgb(9_9_11)_inset,0_0.5px_0_1.5px_#71717a_inset]";
  };

  return (
    <motion.button
      data-slot="command-input-submit"
      layoutId={`command-input-submit-${id}`}
      transition={transition}
      className={cn(
        "z-[1] mr-1 [&_svg:not([class*='size-'])]:size-4 cursor-pointer disabled:pointer-events-none disabled:opacity-50 shrink-0 [&_svg]:shrink-0 outline-none rounded-xl text-xs font-manrope_1 flex items-center justify-center font-medium absolute inset-y-[0.1rem] md:inset-y-1",
        // Position and sizing - only expand for error messages, not success checkmark
        showExpandedMessage ? 'left-0.5 md:left-1 right-0.5 md:right-1 px-4' : 'right-0.5 md:right-1 px-4',
        // Background styling
        getBackgroundClasses(),
        // Text wrapping for messages
        showExpandedMessage ? 'whitespace-normal text-center' : 'whitespace-nowrap',
        className,
      )}
      {...props}
    >
      {showExpandedMessage ? (
        <motion.span
          key="message"
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.3 }}
          className="text-xs leading-tight"
        >
          {message}
        </motion.span>
      ) : showSuccessCheckmark ? (
        <motion.div
          key="success-checkmark"
          initial={{ opacity: 0, scale: 0.5 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.3, type: "spring", stiffness: 300 }}
          className="flex items-center justify-center"
        >
          <Check className="w-4 h-4" />
        </motion.div>
      ) : isSubmitting ? (
        <motion.span
          key="submitting"
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.2 }}
          className="flex items-center gap-2"
        >
          <div className="w-3 h-3 border-2 border-white/30 border-t-white rounded-full animate-spin" />
          <span>Creating...</span>
        </motion.span>
      ) : (
        <motion.span
          key="button-text"
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.2 }}
          className="flex items-center gap-1"
        >
          {children}
          {Icon && <Icon className="size-3" />}
        </motion.span>
      )}
    </motion.button>
  );
}

export {
  CommandInputProvider,
  CommandInput,
  CommandInputField,
  CommandInputSubmit,
  useCommandInput,
};
