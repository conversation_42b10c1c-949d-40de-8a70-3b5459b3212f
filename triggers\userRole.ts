// triggers/userRole.ts
import type { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>rig<PERSON>, RemoveTrigger } from 'blade/types';

// During triggers - required by Blade for write operations
export const during = {
  add: async (...args: any[]) => {
    const [query, _multiple, options] = args;
    const typedQuery = query as any;
    const typedOptions = options as any;

    // Ensure query.with exists
    if (!typedQuery.with) {
      return typedQuery;
    }

    // Handle both single object and array cases
    const processUserRoleData = async (userRoleData: any) => {
      console.log('UserRole during.add trigger - processing data:', userRoleData);

      // Set default values
      userRoleData.createdAt = userRoleData.createdAt || new Date();
      userRoleData.updatedAt = userRoleData.updatedAt || new Date();

      return userRoleData;
    };

    if (Array.isArray(typedQuery.with)) {
      typedQuery.with = await Promise.all(typedQuery.with.map(processUserRoleData));
    } else {
      typedQuery.with = await processUserRoleData(typedQuery.with);
    }

    console.log('UserRole during.add trigger - final query:', typedQuery);
    return typedQuery;
  },

  set: (...args: any[]) => {
    const [query, _multiple, _options] = args;
    const typedQuery = query as any;

    // Ensure query.to exists
    if (!typedQuery.to) {
      return typedQuery;
    }

    // Update timestamp
    typedQuery.to.updatedAt = new Date();

    console.log('UserRole during.set trigger - processed data:', typedQuery.to);
    return typedQuery;
  },

  remove: (...args: any[]) => {
    const [query, _multiple, _options] = args;
    const typedQuery = query as any;

    // Add any validation or cleanup logic for deletions
    console.log('UserRole during.remove trigger called with query:', typedQuery);
    return typedQuery;
  }
};

// Trigger for creating user roles
export const add: AddTrigger = async (...args) => {
  const [query, _multiple, options] = args;
  const typedQuery = query as any;
  const typedOptions = options as any;

  // Ensure query.with exists
  if (!typedQuery.with) {
    return typedQuery;
  }

  // Handle both single object and array cases
  const processUserRoleData = async (userRoleData: any) => {
    console.log('UserRole add trigger - processing data:', userRoleData);

    // Set default values
    userRoleData.createdAt = userRoleData.createdAt || new Date();
    userRoleData.updatedAt = userRoleData.updatedAt || new Date();

    return userRoleData;
  };

  if (Array.isArray(typedQuery.with)) {
    typedQuery.with = await Promise.all(typedQuery.with.map(processUserRoleData));
  } else {
    typedQuery.with = await processUserRoleData(typedQuery.with);
  }

  console.log('UserRole add trigger - final query:', typedQuery);
  return typedQuery;
};

// Trigger for updating user roles
export const set: SetTrigger = (...args) => {
  const [query, _multiple, _options] = args;
  const typedQuery = query as any;

  // Ensure query.to exists
  if (!typedQuery.to) {
    return typedQuery;
  }

  // Update timestamp
  typedQuery.to.updatedAt = new Date();

  console.log('UserRole update trigger - processed data:', typedQuery.to);
  return typedQuery;
};

// Trigger for getting user roles (can be used for access control)
export const get: GetTrigger = (...args) => {
  const [query, _multiple, _options] = args;
  const typedQuery = query as any;

  // Add any access control logic here if needed
  return typedQuery;
};

// Trigger for removing user roles
export const remove: RemoveTrigger = (...args) => {
  const [query, _multiple, _options] = args;
  const typedQuery = query as any;

  // Add any validation or cleanup logic for deletions
  console.log('UserRole removal trigger called with query:', typedQuery);
  return typedQuery;
};
