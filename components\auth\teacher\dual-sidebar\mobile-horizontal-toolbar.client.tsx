'use client';
import { useState, useCallback, useRef, useEffect, useMemo } from "react";
import { motion, AnimatePresence, MotionConfig } from 'motion/react';
import { useHotkeys } from 'react-hotkeys-hook';
import { cn } from "../../../../lib/utils";
import { useRedirect, useParams, useLocation, usePopulatePathname } from 'blade/hooks';

import {
 TooltipProvider,
 AnimatedTooltip
} from '../../../ui/tooltip.client';
import useClickOutside from '../../../../hooks/useClickOutside';

import { UserNav } from '../../shared/dual-sidebar/user-nav.client';
import { LogoutSection } from '../../shared/user-nav-content';

import {
  ArrowLeft,
  X,
  Search
} from "lucide-react";
import {
  CommandInputProvider,
  CommandInput,
  CommandInputField,
  CommandInputSubmit,
} from '../../shared/command-menu/command-input';
import {
  CalendarIcon,
  HouseIcon, 
  SearchIcon, 
  StudentIcon, 
  TeacherIcon, 
  StatusIcon
} from "../../../ui/icons";

interface IconItem {
  id: string;
  icon: any;
  label: string;
  tooltip: string;
  section: number;
  url?: string;
  flyoutHeaderStyle?: "title" | "button";
  hotkey?: string; // Add hotkey property
}

const iconItems: IconItem[] = [
  {
    id: "home",
    icon: HouseIcon,
    label: "Home",
    tooltip: "Home",
    section: 0,
    url: "",
    hotkey: "ctrl+h,cmd+h" // Home hotkey
  },
  {
    id: "calendar",
    icon: CalendarIcon,
    label: "Calendar",
    tooltip: "Calendar",
    section: 1,
    url: "calendar",
    hotkey: "ctrl+1,cmd+1" // Calendar hotkey
  },
  {
    id: "classes",
    icon: TeacherIcon,
    label: "Classes",
    tooltip: "Your Classes",
    section: 3,
    url: "classes",
    hotkey: "ctrl+2,cmd+2" // Classes hotkey
  },
  {
    id: "students-gauge",
    icon: StudentIcon,
    label: "Students",
    tooltip: "Student Overview",
    section: 4,
    url: "students",
    flyoutHeaderStyle: "button",
    hotkey: "ctrl+3,cmd+3" // Students hotkey
  },
  {
    id: "status",
    icon: StatusIcon,
    label: "Status",
    tooltip: "System Status",
    section: 5,
    flyoutHeaderStyle: "title",
    hotkey: "ctrl+4,cmd+4" // Status hotkey
  },
  {
    id: "search",
    icon: SearchIcon,
    label: "Search",
    tooltip: "Search (⌘K)",
    section: 6,
    flyoutHeaderStyle: "title",
    hotkey: "ctrl+k,cmd+k" // Search hotkey - this is the main one we want
  },
];

// FIXED: Dynamic Island inspired spring transition with Apple's exact values
const dynamicIslandTransition = {
  type: 'spring' as const,
  stiffness: 500,
  damping: 35,
  mass: 0.8,
};

interface MobileHorizontalToolbarProps {
  flyout: string | null;
  setFlyout: (flyout: string | null) => void;
  data: any;
  handleToggleFlyout: (itemId: string) => void;
  createRenderFlyoutContent: (setCommandMenuHeight: (height: number) => void) => (flyout: string | null, data: any, setFlyout: (flyout: string | null) => void) => React.ReactNode;
  onSearchToggle?: (isSearchActive: boolean) => void;
  searchValue?: string;
  onSearchValueChange?: (value: string) => void;
  externalSearchActive?: boolean; // Add this new prop
  currentPlaceholder?: string; // Add this new prop
  formSubmitHandler?: (() => void) | null; // Add this new prop
  currentMode?: string; // Add this new prop to track command menu mode
  currentStep?: string; // Add this new prop to track current step
}

export function MobileHorizontalToolbar({
  flyout,
  setFlyout,
  data,
  handleToggleFlyout,
  createRenderFlyoutContent,
  onSearchToggle,
  searchValue = '',
  onSearchValueChange,
  externalSearchActive = false, // Add this new prop
  currentPlaceholder = 'Search commands', // Add this new prop
  formSubmitHandler = null, // Add this new prop
  currentMode = 'default', // Add this new prop
  currentStep = undefined // Add this new prop
}: MobileHorizontalToolbarProps) {

  const [active, setActive] = useState<string | null>(null);
  const contentRef = useRef<HTMLDivElement>(null);
  const menuRef = useRef<HTMLDivElement>(null);
  const ref = useRef<HTMLDivElement>(null);
  const [isOpen, setIsOpen] = useState(false);
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const [isSearchActive, setIsSearchActive] = useState(false);
  const visibleSearchInputRef = useRef<HTMLInputElement>(null);

  const [inputKey, setInputKey] = useState(0); // Force input re-render
  const [isFormReady, setIsFormReady] = useState(false); // Track form readiness

  // Form state for CommandInput component
  const [showInput, setShowInput] = useState(false);
  const [message, setMessage] = useState('');
  const [messageType, setMessageType] = useState<'success' | 'error' | ''>('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  // CRITICAL: Track content height for smooth animations
  const [contentHeight, setContentHeight] = useState(0);
  
  // Command menu height state (now in correct scope)
  const [commandMenuHeight, setCommandMenuHeight] = useState(0);

  // Add state to track when width animation is complete
  const [isWidthAnimationComplete, setIsWidthAnimationComplete] = useState(false);
  
  // Track scroll state for blur gradients
  const [canScrollLeft, setCanScrollLeft] = useState(false);
  const [canScrollRight, setCanScrollRight] = useState(false);
  const [hasOverflow, setHasOverflow] = useState(false);

  // Add hooks for navigation
  const redirect = useRedirect();
  const params = useParams();
  const location = useLocation();
  const populatePathname = usePopulatePathname();

  // FIXED: Stable form handler reference with better change detection
  const formSubmitRef = useRef<(() => void) | null>(null);
  const previousModeRef = useRef(currentMode);
  const previousStepRef = useRef(currentStep);
  const handlerIdRef = useRef<string>('');

  // FIXED: Form readiness with stable handler tracking and change detection
  useEffect(() => {
    const handlerId = `${currentMode}-${currentStep || 'default'}`;

    // Only update when the handler actually changes or mode/step changes
    if (formSubmitHandler !== formSubmitRef.current || handlerId !== handlerIdRef.current) {
      formSubmitRef.current = formSubmitHandler;
      handlerIdRef.current = handlerId;
      setIsFormReady(!!formSubmitHandler && currentMode !== 'default');
      console.log('🎯 Form handler updated for mode:', currentMode, 'step:', currentStep, 'ready:', !!formSubmitHandler);
    }
  }, [currentMode, currentStep, formSubmitHandler]);

  // FIXED: Input key update with stable mode and step tracking
  useEffect(() => {
    // Only update input key when mode or step actually changes
    if (currentMode !== previousModeRef.current || currentStep !== previousStepRef.current) {
      previousModeRef.current = currentMode;
      previousStepRef.current = currentStep;
      if (currentMode !== 'default') {
        setInputKey(prev => prev + 1);
        console.log('🎯 Input key updated for mode/step change:', currentMode, currentStep);
      }
    }
  }, [currentMode, currentStep]);

  // Reset form state when mode or step changes
  useEffect(() => {
    setIsSubmitting(false);
    setMessage('');
    setMessageType('');
  }, [currentMode, currentStep]);

  // Listen for student creation events
  useEffect(() => {
    const handleStudentCreated = (event: CustomEvent) => {
      setMessage('Student added successfully!');
      setMessageType('success');
      setIsSubmitting(false);

      // Clear success message after 3 seconds
      setTimeout(() => {
        setMessage('');
        setMessageType('');
      }, 3000);
    };

    const handleStudentCreationError = (event: CustomEvent) => {
      const errorMessage = event.detail?.error || 'Failed to create student';
      setMessage(errorMessage);
      setMessageType('error');
      setIsSubmitting(false);

      // Clear error message after 4 seconds
      setTimeout(() => {
        setMessage('');
        setMessageType('');
      }, 4000);
    };

    const handleExistingStudentFound = (event: CustomEvent) => {
      setMessage('Student already exists - added to your class!');
      setMessageType('success');
      setIsSubmitting(false);

      // Clear success message after 3 seconds
      setTimeout(() => {
        setMessage('');
        setMessageType('');
      }, 3000);
    };

    window.addEventListener('studentCreated', handleStudentCreated as EventListener);
    window.addEventListener('studentCreationError', handleStudentCreationError as EventListener);
    window.addEventListener('existingStudentFound', handleExistingStudentFound as EventListener);

    return () => {
      window.removeEventListener('studentCreated', handleStudentCreated as EventListener);
      window.removeEventListener('studentCreationError', handleStudentCreationError as EventListener);
      window.removeEventListener('existingStudentFound', handleExistingStudentFound as EventListener);
    };
  }, []);

  // FIXED: Better focus management with stable focus and cursor positioning
  useEffect(() => {
    if (isSearchActive && isWidthAnimationComplete && visibleSearchInputRef.current) {
      const focusInput = () => {
        if (visibleSearchInputRef.current && document.activeElement !== visibleSearchInputRef.current) {
          visibleSearchInputRef.current.focus();
          // CRITICAL: Ensure cursor is visible and at the end
          const inputLength = visibleSearchInputRef.current.value.length;
          visibleSearchInputRef.current.setSelectionRange(inputLength, inputLength);

          // Force cursor visibility with styles
          visibleSearchInputRef.current.style.caretColor = 'currentColor';
          visibleSearchInputRef.current.style.outline = 'none';

          console.log('🎯 Input focused with cursor at position:', inputLength);
        }
      };

      // Initial focus
      const timer = setTimeout(focusInput, currentMode !== 'default' ? 200 : 100);

      // FIXED: Add interval to maintain focus if it gets lost
      const focusInterval = setInterval(() => {
        if (isSearchActive && visibleSearchInputRef.current && document.activeElement !== visibleSearchInputRef.current) {
          console.log('🔄 Refocusing lost input');
          focusInput();
        }
      }, 500); // Check every 500ms

      return () => {
        clearTimeout(timer);
        clearInterval(focusInterval);
      };
    }
  }, [isSearchActive, isWidthAnimationComplete, currentMode, inputKey]);

   // Sync external search state with internal state
  useEffect(() => {
    if (externalSearchActive !== isSearchActive) {
      console.log('🔄 Syncing external search state:', externalSearchActive);
      setIsSearchActive(externalSearchActive);
      searchIntentRef.current = externalSearchActive;
    }
  }, [externalSearchActive, isSearchActive]);

  // ESC to close search/flyouts
  useHotkeys('escape', (event) => {
    if (isSearchActive) {
      event.preventDefault();
      console.log('🔥 ESC - Closing search');
      setIsSearchActive(false);
      searchIntentRef.current = false;
      onSearchToggle?.(false);
    } else if (isOpen) {
      event.preventDefault();
      console.log('🔥 ESC - Closing flyout');
      setIsOpen(false);
      setActive(null);
      setFlyout(null);
    }
  }, {
    enableOnFormTags: true, // Allow ESC even in form elements
    preventDefault: false // Only prevent default when we handle it
  });

  // Check if content overflows and update scroll indicators
  const checkScrollState = useCallback(() => {
    if (scrollContainerRef.current) {
      const container = scrollContainerRef.current;
      const scrollLeft = container.scrollLeft;
      const scrollWidth = container.scrollWidth;
      const clientWidth = container.clientWidth;
      
      // Check if content actually overflows
      const contentOverflows = scrollWidth > clientWidth;
      setHasOverflow(contentOverflows);
      
      if (contentOverflows) {
        setCanScrollLeft(scrollLeft > 0);
        setCanScrollRight(scrollLeft < scrollWidth - clientWidth - 1); // -1 for rounding
      } else {
        setCanScrollLeft(false);
        setCanScrollRight(false);
      }
    }
  }, []);

  // Monitor scroll state
  useEffect(() => {
    const container = scrollContainerRef.current;
    if (container) {
      // Initial check
      checkScrollState();
      
      // Add scroll listener
      container.addEventListener('scroll', checkScrollState);
      
      // Add resize observer to handle layout changes
      const resizeObserver = new ResizeObserver(checkScrollState);
      resizeObserver.observe(container);
      
      return () => {
        container.removeEventListener('scroll', checkScrollState);
        resizeObserver.disconnect();
      };
    }
  }, [checkScrollState]);

  // Track width animation completion
  useEffect(() => {
    if (isSearchActive) {
      // Reset completion state when search starts
      setIsWidthAnimationComplete(false);
      
      // Set completion after animation duration (based on spring physics)
      // With stiffness 1000, damping 60, mass 0.2 - animation completes in ~150ms
      // Adding extra delay so search input appears at 200ms total
      const timer = setTimeout(() => {
        setIsWidthAnimationComplete(true);
      }, 200);
      
      return () => clearTimeout(timer);
    } else {
      // When search closes, immediately set to false
      setIsWidthAnimationComplete(false);
    }
  }, [isSearchActive]);
  
  // FIXED: Add ref to track search intent immediately
  const searchIntentRef = useRef(false);

  // Get all icon items sorted by section (Home first, then others)
  const toolbarItems = iconItems.sort((a, b) => a.section - b.section);

  // Updated flyout width config with mobile-responsive dimensions
  const flyoutWidthConfig = useMemo(() => ({
    'search': { mobile: 'min(90vw, 350px)', desktop: '600px' },
    'default': { mobile: 'min(90vw, 400px)', desktop: '550px' }
  }), []);

  // Enhanced getDynamicWidth - simplified for the new scrollable layout
  const getDynamicWidth = useCallback(() => {
    if (isSearchActive) {
      // When search is active, expand to accommodate search input
      if (typeof window !== 'undefined') {
        const screenWidth = window.innerWidth;
        
        if (screenWidth < 438) {
          return `${screenWidth * 0.95}px`; // 95% of screen width
        } else if (screenWidth < 768) {
          return '400px';
        } else {
          return '500px';
        }
      }
      return '350px'; // Fallback
    }
    
    if (!isOpen || !active) {
      // Fixed width for normal toolbar state
      if (typeof window !== 'undefined') {
        const screenWidth = window.innerWidth;
        if (screenWidth < 391) {
          return '94vw'; // Very small screens - use viewport width
        } else if (screenWidth < 768) {
          return '85vw'; // Mobile screens - use viewport width for responsiveness
        } else {
          return '380px'; // Desktop screens - fixed width
        }
      }
      return '94vw'; // Fallback
    }

    // Flyout width
    const config = flyoutWidthConfig[active as keyof typeof flyoutWidthConfig] || flyoutWidthConfig.default;
    return `clamp(280px, ${config.mobile}, ${config.desktop})`;
  }, [isOpen, active, isSearchActive, flyoutWidthConfig]);

  // CRITICAL: Reset intent ref when search state changes
  useEffect(() => {
    // Sync the intent ref with the actual state after state updates
    searchIntentRef.current = isSearchActive;
  }, [isSearchActive]);

  useEffect(() => {
    if (contentRef.current && isOpen) {
      const resizeObserver = new ResizeObserver(entries => {
        for (let entry of entries) {
          if (isOpen) {
            const newHeight = entry.contentRect.height;
            
            // For search flyout, use the command menu specific height if available
            if (active === 'search' && commandMenuHeight > 0) {
              setContentHeight(commandMenuHeight + 32); // Add padding
            } else {
              setContentHeight(newHeight);
            }
          }
        }
      });
      
      resizeObserver.observe(contentRef.current);
      return () => resizeObserver.disconnect();
    } else {
      setContentHeight(0);
      const timeoutId = setTimeout(() => setContentHeight(0), 10);
      return () => clearTimeout(timeoutId);
    }
  }, [isOpen, active, commandMenuHeight]);

  // Create the render function with access to setCommandMenuHeight
  const renderFlyoutContent = useMemo(() => {
    return createRenderFlyoutContent(setCommandMenuHeight);
  }, [createRenderFlyoutContent, setCommandMenuHeight]);

  // Enhanced handleToggleFlyout with immediate state change
  const enhancedHandleToggleFlyout = useCallback((itemId: string) => {
    handleToggleFlyout(itemId);
  }, [handleToggleFlyout]);

  const handleItemClick = (item: IconItem) => {
    // For home item, close any open flyouts and navigate
    if (item.id === 'home') {
      setIsOpen(false);
      setActive(null);
      setFlyout(null);

      // Navigate to home
      const slug = Array.isArray(params['slug']) ? params['slug'][0] : params['slug'];
      const fullUrl = `/teacher/${slug}`;
      redirect(fullUrl);
      return;
    }

    // For navigation items with URLs - NEVER toggle flyout on second click
    if (item.url) {
      const slug = Array.isArray(params['slug']) ? params['slug'][0] : params['slug'];
      const fullUrl = item.url.startsWith('/')
        ? item.url
        : `/teacher/${slug}/${item.url}`;

      // Always navigate, never toggle flyout for navigation items
      console.log('🎯 Mobile navigating to:', fullUrl, 'for item:', item.id);
      redirect(fullUrl);
      return;
    }

    // For items without URLs that aren't search - no flyout toggle behavior
    console.log('🎯 Non-navigation item clicked:', item.id);
  };

  // Handle search icon click - with immediate width intent
  const handleSearchClick = () => {
    const newSearchState = !isSearchActive;
    
    // CRITICAL: Set intent immediately before state update
    searchIntentRef.current = newSearchState;
    
    // Close any other flyouts when opening search
    if (newSearchState) {
      setIsOpen(false);
      setActive(null);
      setFlyout(null);
    }
    
    // Set search state and notify parent
    setIsSearchActive(newSearchState);
    onSearchToggle?.(newSearchState);
  };

  // Sync intent ref with actual search state
  useEffect(() => {
    searchIntentRef.current = isSearchActive;
  }, [isSearchActive]);

  // In mobile-horizontal-toolbar.client.tsx
  // Update the useClickOutside hook to exclude command menu clicks
  useClickOutside(ref as React.RefObject<Element>, (event) => {
    // Don't close if clicking on UserNav or its children
    const target = event.target as Element;
    const userNavElement = ref.current?.querySelector('[data-user-nav]');
    
    if (userNavElement && userNavElement.contains(target)) {
      return;
    }
    
    // CRITICAL: Don't close if clicking inside the command menu
    // The command menu is rendered in a portal, so we need to check for it specifically
    const commandMenuElements = document.querySelectorAll('[cmdk-root], [data-command-menu]');
    const isClickingInsideCommandMenu = Array.from(commandMenuElements).some(element => 
      element.contains(target)
    );
    
    if (isClickingInsideCommandMenu) {
      console.log('🚫 Click inside command menu detected - not closing');
      return;
    }
    
    // Also check for any element with command menu related classes
    const isCommandMenuRelated = target.closest('[cmdk-root]') || 
                                target.closest('[data-command-menu]') ||
                                target.closest('.command-menu') ||
                                target.closest('[role="combobox"]') ||
                                target.closest('[role="listbox"]');
    
    if (isCommandMenuRelated) {
      console.log('🚫 Click on command menu related element - not closing');
      return;
    }
    
    // Close search and flyouts
    console.log('👆 Click outside detected - closing search/flyouts');
    setIsOpen(false);
    setActive(null);
    setIsSearchActive(false);
    searchIntentRef.current = false;
    onSearchToggle?.(false);
  });

  // Sync with parent flyout state
  useEffect(() => {
    setActive(flyout);
    setIsOpen(!!flyout);
  }, [flyout]);

  return (
    <TooltipProvider animated={true} openDelay={300} animatedCloseDelay={150}>
      <MotionConfig transition={dynamicIslandTransition}>
        {/* FIXED: Better mobile positioning with responsive padding */}
        <div className="fixed bottom-4 left-1/2 transform -translate-x-1/2 z-[100000004] px-4" ref={ref}>
          {/* Main container */}
          <motion.div
            className="bg-[#eeeeee] dark:bg-[#171719] text-zinc-800 shadow-[0_-1px_0_1px_rgba(255,255,255,0.8)_inset,0_0_0_1px_rgb(220,220,220)_inset,0_0.5px_0_1.5px_#ccc_inset] dark:text-zinc-50 dark:shadow-[0_-1px_0_1px_rgba(0,0,0,0.8)_inset,0_0_0_1px_rgb(9_9_11)_inset,0_0.5px_0_1.5px_#71717a_inset]"
            initial={false}
            animate={{
              width: getDynamicWidth(),
            }}
            transition={{
              // FIXED: Nearly instant animation - aggressive spring physics
              type: 'spring',
              stiffness: 1000,  // Much higher stiffness for near-instant response
              damping: 60,      // High damping to prevent any bounce
              mass: 0.2         // Very low mass for immediate response
            }}
            style={{
              overflow: "hidden",
              // FIXED: Check intent ref first for truly instant border radius change
              borderRadius: (isOpen || isSearchActive || searchIntentRef.current) ? "12px" : "32px",
            }}
          >
            {/* Content container with precise height animation - only for flyouts, not search */}
            <AnimatePresence initial={false}>
              {isOpen && !isSearchActive && (
                <motion.div
                  key="content"
                  initial={{ 
                    height: 0,
                    opacity: 0 
                  }}
                  animate={{ 
                    height: isOpen && contentHeight > 0 ? contentHeight : 0,
                    opacity: isOpen ? 1 : 0
                  }}
                  exit={{ 
                    height: 0,
                    opacity: 0
                  }}
                  transition={{
                    ...dynamicIslandTransition,
                    height: {
                      type: 'spring',
                      stiffness: 600,
                      damping: 40,
                      mass: 0.7
                    },
                    opacity: {
                      duration: isOpen ? 0.2 : 0.1,
                      ease: "easeOut"
                    }
                  }}
                  style={{
                    overflow: "hidden"
                  }}
                >
                  <div 
                    ref={contentRef} 
                    className="p-2"
                  >
                    {toolbarItems
                      .filter(item => item.id !== 'search')
                      .map((item) => {
                        const isSelected = active === item.id;
                        return (
                          <div
                            key={item.id}
                            className={cn(
                              'px-2 pt-2 text-sm overflow-y-auto custom-scrollbar max-h-[70vh]',
                              isSelected ? 'block' : 'hidden'
                            )}
                          >
                            {isSelected && renderFlyoutContent(item.id, data, setFlyout)}
                          </div>
                        );
                      })}
                  </div>
                </motion.div>
              )}
            </AnimatePresence>

            {/* Toolbar content */}
            <div className="flex items-center md:h-16 h-14 flex-shrink-0">
              <AnimatePresence mode="wait">
                {isOpen && !isSearchActive ? (
                  // Back Arrow Mode for flyouts
                  <motion.div
                    key="back-arrow"
                    initial={{ opacity: 0, scale: 0.8 }}
                    animate={{ opacity: 1, scale: 1 }}
                    exit={{ opacity: 0, scale: 0.8 }}
                    transition={{
                      type: 'spring',
                      stiffness: 500,
                      damping: 35,
                      mass: 0.8,
                    }}
                    className={cn(
                      "flex items-center w-full px-4",
                      active === 'user-nav' ? "justify-between" : "justify-between"
                    )}
                  >
                    {/* Back Arrow Button */}
                    <button
                      onClick={() => {
                        setIsOpen(false);
                        setActive(null);
                        setFlyout(null);
                      }}
                      className="font-manrope_1 flex items-center gap-2 text-zinc-700 dark:text-zinc-300 hover:text-zinc-900 dark:hover:text-zinc-100 transition-colors touch-manipulation flex-shrink-0"
                      aria-label="Go back to toolbar"
                    >
                      <div className="flex items-center justify-center w-8 h-8 rounded-full hover:bg-zinc-100 dark:hover:bg-zinc-800 transition-colors">
                        <ArrowLeft className="w-5 h-5" />
                      </div>
                      <span className="text-sm font-medium">Back</span>
                    </button>

                    {/* Current Flyout Title */}
                    <div className={cn(
                      active === 'user-nav' ? "flex-1 text-center" : "flex-1 text-right"
                    )}>
                      <h3 className="text-sm font-manrope_1 font-semibold text-zinc-800 dark:text-zinc-200">
                        {active === 'user-nav' ? 'Account' :
                         active === 'calendar' ? 'Calendar' :
                         active === 'classes' ? 'Classes' :
                         'Menu'}
                      </h3>
                    </div>

                    {/* Conditional Right Side Content */}
                    {active === 'user-nav' ? (
                      <div className="flex-shrink-0">
                        <LogoutSection onLogout={() => setFlyout(null)} />
                      </div>
                    ) : (
                      <div className="w-16 flex-shrink-0" />
                    )}
                  </motion.div>
                ) : isSearchActive && isWidthAnimationComplete ? (
                  // Search Input Mode - Only show after width animation is complete (200ms delay)
                  currentMode !== 'default' ? (
                    // Form Input Mode - Use CommandInput component
                    <motion.div
                      key={`form-input-${handlerIdRef.current}`} // FIXED: Use stable handler ID
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      transition={{ duration: 0.15, ease: "easeOut" }}
                      className="w-full"
                    >
                      <CommandInputProvider
                        showInput={true} // Always show input in form mode
                        setShowInput={setShowInput}
                        className="w-full"
                      >
                        <CommandInput>
                          <CommandInputField
                            ref={visibleSearchInputRef}
                            key={`input-${handlerIdRef.current}`} // FIXED: Use stable handler ID for key
                            placeholder={currentPlaceholder}
                            value={searchValue}
                            onChange={(e) => {
                              console.log('🎯 Input changing to:', e.target.value);
                              onSearchValueChange?.(e.target.value);
                            }}
                            onEnterPress={() => {
                              console.log('🔥 onEnterPress - formReady:', isFormReady, 'handler:', !!formSubmitRef.current);
                              if (formSubmitRef.current && searchValue.trim()) {
                                console.log('🚀 Calling form submit handler from onEnterPress');
                                setIsSubmitting(true);
                                formSubmitRef.current();
                              }
                            }}
                            onKeyDown={(e) => {
                              console.log('🎯 Key pressed:', e.key, 'formReady:', isFormReady);
                              
                              if (e.key === 'Escape') {
                                e.preventDefault();
                                setIsSearchActive(false);
                                searchIntentRef.current = false;
                                onSearchToggle?.(false);
                                return;
                              }
                              
                              if (e.key === 'Enter') {
                                e.preventDefault();
                                e.stopPropagation();
                                console.log('🔥 Enter key - handler exists:', !!formSubmitRef.current, 'value:', searchValue);
                                
                                if (formSubmitRef.current && searchValue.trim()) {
                                  console.log('🚀 Calling form submit handler from Enter key');
                                  setIsSubmitting(true);
                                  formSubmitRef.current();
                                } else {
                                  console.warn('⚠️ Cannot submit - no handler or empty value');
                                }
                                return;
                              }
                              
                                                            if (e.key === 'ArrowDown' || e.key === 'ArrowUp') {
                                e.preventDefault();
                                const commandMenu = document.querySelector('[cmdk-root]');
                                if (commandMenu) {
                                  const event = new KeyboardEvent('keydown', {
                                    key: e.key,
                                    code: e.code,
                                    bubbles: true,
                                    cancelable: true
                                  });
                                  commandMenu.dispatchEvent(event);
                                }
                              }
                            }}
                            // CRITICAL: Add these props for stable input behavior
                            autoComplete="off"
                            autoCorrect="off"
                            autoCapitalize="off"
                            spellCheck={false}
                            style={{
                              caretColor: 'currentColor',
                              outline: 'none',
                              WebkitAppearance: 'none', // Prevent iOS styling
                              WebkitTapHighlightColor: 'transparent' // Remove tap highlight
                            }}
                          />
                          <CommandInputSubmit
                            onClick={() => {
                              console.log('🔥 Submit button clicked - handler:', !!formSubmitRef.current, 'value:', searchValue);
                              if (formSubmitRef.current && searchValue.trim()) {
                                console.log('🚀 Calling form submit handler from button click');
                                setIsSubmitting(true);
                                formSubmitRef.current();
                              }
                            }}
                            message={message}
                            messageType={messageType}
                            isSubmitting={isSubmitting}
                          >
                            {currentMode === 'add-student'
                              ? (currentStep === 'name' ? 'Next' : 'Add Student')
                              : 'Create'}
                          </CommandInputSubmit>
                        </CommandInput>
                      </CommandInputProvider>
                    </motion.div>
                  ) : (
                    // Default Search Mode - Use regular input
                    <motion.div
                      key="search-input"
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      transition={{ duration: 0.15, ease: "easeOut" }}
                      className="flex items-center w-full px-4"
                    >
                      <input
                        ref={visibleSearchInputRef}
                        type="text"
                        placeholder={currentPlaceholder}
                        value={searchValue}
                        onChange={(e) => onSearchValueChange?.(e.target.value)}
                        onKeyDown={(e) => {
                          if (e.key === 'Escape') {
                            setIsSearchActive(false);
                            searchIntentRef.current = false;
                            onSearchToggle?.(false);
                          } else if (e.key === 'Enter') {
                            e.preventDefault();
                            const commandMenu = document.querySelector('[cmdk-root]');
                            if (commandMenu) {
                              const event = new KeyboardEvent('keydown', {
                                key: e.key,
                                code: e.code,
                                bubbles: true,
                                cancelable: true
                              });
                              commandMenu.dispatchEvent(event);
                            }
                          } else if (e.key === 'ArrowDown' || e.key === 'ArrowUp') {
                            e.preventDefault();
                            const commandMenu = document.querySelector('[cmdk-root]');
                            if (commandMenu) {
                              const event = new KeyboardEvent('keydown', {
                                key: e.key,
                                code: e.code,
                                bubbles: true,
                                cancelable: true
                              });
                              commandMenu.dispatchEvent(event);
                            }
                          }
                        }}
                        className="flex-1 font-manrope_1 bg-transparent border-none outline-none text-sm placeholder:text-zinc-500 dark:placeholder:text-zinc-400 text-zinc-900 dark:text-zinc-100"
                        autoFocus
                        autoComplete="off"
                        autoCorrect="off"
                        autoCapitalize="off"
                        spellCheck={false}
                        style={{
                          caretColor: 'currentColor'
                        }}
                      />
                      <button
                        onClick={() => {
                          setIsSearchActive(false);
                          searchIntentRef.current = false;
                          onSearchToggle?.(false);
                        }}
                        className="p-1 hover:bg-zinc-100 dark:hover:bg-zinc-800 rounded"
                      >
                        <X className="h-4 w-4 text-zinc-500" />
                      </button>
                    </motion.div>
                  )
                ) : !isSearchActive && !searchIntentRef.current ? (
                  // Normal Toolbar Mode with Scrollable Navigation
                  <motion.div
                    key="normal-toolbar"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    exit={{ opacity: 0 }}
                    transition={{
                      // FIXED: Much faster exit for instant hiding when search activates
                      duration: isSearchActive || searchIntentRef.current ? 0.05 : 0.2,
                      ease: "easeOut"
                    }}
                    className="flex items-center w-full h-full"
                  >
                    {/* Scrollable Navigation Section */}
                    <div className="flex-1 relative">
                      {/* Fixed width container with horizontal scrolling */}
                      <div className="flex h-auto gap-1 justify-center">
                        <div className="container flex h-auto w-full max-w-[240px] md:max-w-[320px] flex-nowrap overflow-x-auto rounded-lg px-px relative">
                          {/* Left blur gradient - only show when can scroll left */}
                          {hasOverflow && canScrollLeft && (
                            <div className={cn(
                              "absolute left-0.5 top-1.5 bottom-1 w-6 z-10 pointer-events-none backdrop-blur-sm",
                              (isOpen || isSearchActive || searchIntentRef.current) ? "rounded-l-xl" : "rounded-l-full"
                            )} />
                          )}

                          {/* Right blur gradient - only show when can scroll right */}
                          {hasOverflow && canScrollRight && (
                            <div className={cn(
                              "absolute right-0 top-2 bottom-2 w-6 z-10 pointer-events-none backdrop-blur-sm",
                              (isOpen || isSearchActive || searchIntentRef.current) ? "rounded-r-xl" : "rounded-r-none"
                            )} />
                          )}

                          <div
                            ref={scrollContainerRef}
                            className="flex space-x-2 p-2 overflow-x-auto [&::-webkit-scrollbar]:hidden w-full"
                            style={{
                              scrollbarWidth: 'none',
                              msOverflowStyle: 'none',
                            } as React.CSSProperties}
                          >
                            <div className="flex space-x-2 min-w-max">
                              {/* Navigation Icons - scrollable */}
                              {toolbarItems
                                .filter(item => item.id !== 'search')
                                .map((item) => {
                                  const isCurrentPageForItem = (item: IconItem) => {
                                    const currentPath = populatePathname(location.pathname);
                                    const slug = Array.isArray(params['slug']) ? params['slug'][0] : params['slug'];
                                    
                                    if (item.id === 'home' || item.url === '') {
                                      const homePattern = `/teacher/${slug}`;
                                      return currentPath === homePattern || currentPath === `${homePattern}/`;
                                    }
                                    
                                    if (!item.url) return false;
                                    
                                    const expectedPath = item.url.startsWith('/')
                                      ? item.url
                                      : `/teacher/${slug}/${item.url}`;
                                    return currentPath === expectedPath;
                                  };

                                  const isCurrentPageInstant = isCurrentPageForItem(item);

                                  return (
                                    <AnimatedTooltip 
                                      key={item.id}
                                      content={isSearchActive ? null : (
                                        <div className="flex gap-2 items-center justify-center">
                                          <p>{item.label}</p>
                                          {item.hotkey && (
                                            <p className="text-xs opacity-70">
                                              {item.hotkey.replace('cmd+', '⌘').replace('ctrl+', 'Ctrl+')}
                                            </p>
                                          )}
                                        </div>
                                      )}
                                      side="top"
                                      sideOffset={14}
                                    >
                                      <button
                                        aria-label={item.label}
                                        className={cn(
                                          'relative flex md:p-1 shrink-0 scale-100 select-none appearance-none items-center justify-center rounded-full transition-colors hover:bg-zinc-100 dark:hover:bg-zinc-800 focus-visible:ring-2 active:scale-[0.98]',
                                          'touch-manipulation outline-none',
                                          // Desktop: larger buttons (h-10 w-10), Mobile: smaller (h-8 w-8)
                                          'md:h-10 md:w-10 h-8 w-8',
                                          active === item.id
                                            ? 'bg-gradient-to-br from-zinc-100 to-zinc-200 text-zinc-800 shadow-[0_-1px_0_1px_rgba(255,255,255,0.8)_inset,0_0_0_1px_rgb(220,220,220)_inset,0_0.5px_0_1.5px_#ccc_inset] dark:bg-gradient-to-b dark:from-[#212026] dark:via-[#212026] dark:to-[#29282e] dark:text-zinc-50 dark:shadow-[0_-1px_0_1px_rgba(0,0,0,0.8)_inset,0_0_0_1px_rgb(9_9_11)_inset,0_0.5px_0_1.5px_#71717a_inset]'
                                            : 'text-zinc-500 dark:text-zinc-400 hover:text-zinc-900 dark:hover:text-zinc-100'
                                        )}
                                        type='button'
                                        onClick={(e) => {
                                          // Auto-scroll to center the clicked item
                                          if (scrollContainerRef.current) {
                                            const container = scrollContainerRef.current;
                                            const buttonElement = e.currentTarget;
                                            const containerRect = container.getBoundingClientRect();
                                            const buttonRect = buttonElement.getBoundingClientRect();

                                            // Calculate the position to center the button
                                            const containerCenter = containerRect.width / 2;
                                            const buttonCenter = buttonRect.left - containerRect.left + buttonRect.width / 2;
                                            const scrollOffset = buttonCenter - containerCenter;

                                            container.scrollTo({
                                              left: container.scrollLeft + scrollOffset,
                                              behavior: 'smooth'
                                            });
                                          }

                                          // Handle the item click
                                          handleItemClick(item);
                                        }}
                                      >
                                        <item.icon
                                          className={cn(
                                            "transition-colors",
                                            // Desktop: larger icons (h-5 w-5), Mobile: smaller (h-4 w-4)
                                            "h-6 w-6",
                                            active === item.id
                                              ? "text-zinc-800 dark:text-zinc-200"
                                              : isCurrentPageInstant
                                              ? "text-black dark:text-white"
                                              : "text-zinc-500 dark:text-zinc-400 hover:text-zinc-900 dark:hover:text-zinc-100"
                                          )}
                                        />
                                      </button>
                                    </AnimatedTooltip>
                                  );
                                })}
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Fixed Right Section - User Nav, Divider, and Search */}
                    <div className="flex items-center gap-2 px-2">
                      {/* Vertical Divider */}
                      <div className="h-6 w-px bg-zinc-300 dark:bg-zinc-600" />
                      
                      {/* User Avatar - Fixed position */}
                      <AnimatedTooltip
                        content={isSearchActive ? null : <div className="flex gap-2 items-center justify-center"><p>Profile</p></div>}
                        side="top"
                        sideOffset={14}
                      >
                        <button
                          type="button"
                          className={cn(
                            'relative flex shrink-0 scale-100 select-none appearance-none items-center justify-center rounded-full transition-colors hover:bg-zinc-100 dark:hover:bg-zinc-800 focus-visible:ring-2 active:scale-[0.98] touch-manipulation outline-none',
                            // Desktop: larger buttons (h-10 w-10), Mobile: smaller (h-8 w-8)
                            'md:h-10 md:w-10 h-8 w-8 p-1',
                            'text-zinc-500 dark:text-zinc-400 hover:text-zinc-900 dark:hover:text-zinc-100'
                          )}
                          data-user-nav
                          aria-label="User profile"
                          onClick={() => {
                            // Close any open flyouts
                            setIsOpen(false);
                            setActive(null);
                            setFlyout(null);
                            setIsSearchActive(false);
                            searchIntentRef.current = false;
                            onSearchToggle?.(false);

                            // Navigate to profile page with teacher slug
                            const slug = Array.isArray(params['slug']) ? params['slug'][0] : params['slug'];
                            const profileUrl = `/teacher/${slug}/profile`;
                            redirect(profileUrl);
                          }}
                        >
                          <div className="md:scale-90 scale-75" style={{ pointerEvents: 'none' }}>
                            <UserNav />
                          </div>
                        </button>
                      </AnimatedTooltip>
                      
                      {/* Vertical Divider */}
                      <div className="h-6 w-px bg-zinc-300 dark:bg-zinc-600" />
                      
                      {/* Search Icon - Fixed position */}
                      <AnimatedTooltip
                        content={isSearchActive ? null : (
                          <div className="flex gap-2 items-center justify-center">
                            <p>Search</p>
                            <p className="text-xs opacity-70">⌘K</p>
                          </div>
                        )}
                        side="top"
                        sideOffset={14}
                      >
                        <button
                          aria-label="Search"
                          className={cn(
                            'relative flex md:p-1 shrink-0 scale-100 select-none appearance-none items-center justify-center rounded-full transition-colors hover:bg-zinc-100 dark:hover:bg-zinc-800 focus-visible:ring-2 active:scale-[0.98]',
                            'touch-manipulation outline-none',
                            // Desktop: larger buttons (h-10 w-10), Mobile: smaller (h-8 w-8)
                            'md:h-10 md:w-10 h-8 w-8',
                            isSearchActive 
                              ? 'bg-gradient-to-br from-zinc-100 to-zinc-200 text-zinc-800 shadow-[0_-1px_0_1px_rgba(255,255,255,0.8)_inset,0_0_0_1px_rgb(220,220,220)_inset,0_0.5px_0_1.5px_#ccc_inset] dark:bg-gradient-to-b dark:from-[#212026] dark:via-[#212026] dark:to-[#29282e] dark:text-zinc-50 dark:shadow-[0_-1px_0_1px_rgba(0,0,0,0.8)_inset,0_0_0_1px_rgb(9_9_11)_inset,0_0.5px_0_1.5px_#71717a_inset]'
                              : 'text-zinc-500 dark:text-zinc-400 hover:text-zinc-900 dark:hover:text-zinc-100'
                          )}
                          type='button'
                          onClick={handleSearchClick}
                        >
                          <Search className={cn(
                            "h-6 w-6 transition-colors",
                            isSearchActive
                              ? "text-zinc-800 dark:text-zinc-200"
                              : "text-zinc-500 dark:text-zinc-400 hover:text-zinc-900 dark:hover:text-zinc-100"
                          )} />
                        </button>
                      </AnimatedTooltip>
                    </div>
                  </motion.div>
                ) : null}
              </AnimatePresence>
            </div>
          </motion.div>
        </div>
      </MotionConfig>
    </TooltipProvider>
  );
}